import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

final ThemeData myCustomTheme = ThemeData(
  // General colors
  primaryColor: Colors.black,
  scaffoldBackgroundColor: Colors.white,
  fontFamily: GoogleFonts.inter().fontFamily,
  colorScheme: ColorScheme.light(
    primary: Colors.black,
    secondary: Colors.grey,
    surface: Colors.white,
    onPrimary: Colors.white,
    onSecondary: Colors.black,
  ),

  // AppBar
  appBarTheme: AppBarTheme(
    backgroundColor: Colors.white,
    elevation: 0,
    iconTheme: IconThemeData(color: Colors.black),
    titleTextStyle: TextStyle(color: Colors.black, fontSize: 20),
  ),

  // Buttons
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: Colors.black,
      foregroundColor: Colors.white,
      enableFeedback: false,
      elevation: 0,
      // how to diable shadow when i press ?
      shadowColor: Colors.transparent,
      splashFactory: NoSplash.splashFactory,
      visualDensity: VisualDensity(horizontal: 0, vertical: 0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      padding: EdgeInsets.symmetric(horizontal: 30, vertical: 18),
    ),
  ),
  textButtonTheme: TextButtonThemeData(
    style: TextButton.styleFrom(foregroundColor: Colors.black),
  ),
  outlinedButtonTheme: OutlinedButtonThemeData(
    style: OutlinedButton.styleFrom(
      foregroundColor: Colors.black,
      side: BorderSide(color: Colors.black),
    ),
  ),

  // Text inputs
  inputDecorationTheme: InputDecorationTheme(
    filled: true,
    fillColor: Colors.grey.shade100,
    border: OutlineInputBorder(),
    focusedBorder: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.black),
    ),
    labelStyle: TextStyle(color: Colors.black),
  ),

  // Text
  textTheme: TextTheme(
    bodyMedium: TextStyle(color: Colors.black, fontSize: 16),
    titleLarge: TextStyle(
      color: Colors.black,
      fontSize: 22,
      fontWeight: FontWeight.bold,
    ),
  ),

  // Cards
  cardTheme: CardThemeData(
    elevation: 2,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    color: Colors.white,
  ),

  // Checkboxes, radios, switches
  checkboxTheme: CheckboxThemeData(
    fillColor: MaterialStateProperty.all(Colors.black),
  ),
  radioTheme: RadioThemeData(
    fillColor: MaterialStateProperty.all(Colors.black),
  ),
  switchTheme: SwitchThemeData(
    thumbColor: MaterialStateProperty.all(Colors.black),
    trackColor: MaterialStateProperty.all(Colors.grey),
  ),

  // SnackBars
  snackBarTheme: SnackBarThemeData(
    backgroundColor: Colors.black,
    contentTextStyle: TextStyle(color: Colors.white),
  ),
);
